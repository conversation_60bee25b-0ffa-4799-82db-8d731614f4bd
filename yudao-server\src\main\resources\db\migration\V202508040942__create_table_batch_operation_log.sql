-- 为 insurance_work_order2 表添加追踪字段
ALTER TABLE hz_insurance.insurance_work_order2
ADD COLUMN `original_status` TINYINT(4) NULL COMMENT '批量变更前的原始状态，用于恢复机制' AFTER `status`,
ADD COLUMN `batch_operation_id` VARCHAR(100) NULL COMMENT '批量操作的唯一批次号，用于审计和追踪' AFTER `original_status`;

-- 创建新的批量操作日志表
CREATE TABLE `insurance_batch_operation_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
  `batch_id` VARCHAR(100) NOT NULL COMMENT '唯一批次号',
  `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型 (例如: BATCH_REJECT, BATCH_RECOVER)',
  `status` VARCHAR(20) NOT NULL COMMENT '操作状态 (例如: COMPLETED, FAILED)',
  `parameters` JSON NULL COMMENT '操作执行时的参数 (例如: { "cutoffDate": "2024-12-31" })',
  `processed_count` INT NOT NULL DEFAULT 0 COMMENT '成功处理的工单数量',
  `operator_id` VARCHAR(64) NULL COMMENT '操作员ID',
  `operator_name` VARCHAR(100) NULL COMMENT '操作员姓名',
  `start_time` DATETIME NOT NULL COMMENT '操作开始时间',
  `end_time` DATETIME NULL COMMENT '操作结束时间',
  `remarks` TEXT NULL COMMENT '备注 (例如: 记录错误信息)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_id` (`batch_id`)
) ENGINE=InnoDB COMMENT='批量操作日志表';
