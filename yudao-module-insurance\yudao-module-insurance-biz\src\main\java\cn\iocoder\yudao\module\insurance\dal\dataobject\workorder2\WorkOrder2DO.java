package cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工单 DO
 *
 * <AUTHOR>
 */
@TableName("insurance_work_order2")
@KeySequence("insurance_work_order2_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrder2DO extends BaseDO {

    /**
     * 标识
     */
    @TableId
    private Long id;
    /**
     * 医院代码
     */
    private String hospitalCode;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 医院等级
     */
    private String hospitalLevel;
    /**
     * 就诊流水号类型：	0-门急诊；	1-住院；
     */
    private Integer treatmentSerialNumberType;
    /**
     * 就诊流水号
     */
    private String treatmentSerialNumber;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 身份证号码
     */
    private String idCardNumber;
    /**
     * 身份证有效期
     */
    private Date idCardValidityPeriod;
    /**
     * 地址
     */
    private String address;
    /**
     * 移动电话
     */
    private String mobilePhoneNumber;
    /**
     * 固定电话号码
     */
    private String fixedPhoneNumber;
    /**
     * 联系人姓名
     */
    private String contactPersonName;
    /**
     * 联系人电话号码
     */
    private String contactPersonPhoneNumber;
    /**
     * 社保类型：	1-城镇职工基本医疗保险；	2-城镇居民基本医疗保险；	3-新型农村合作医疗；	4-贫困救助；	5-商业医疗保险；	6-全公费；	7-全自费；	8-其他社会保险；	9-其他；
     */
    private String socialMedicareType;
    /**
     * 社保卡号
     */
    private String socialMedicareCardNumber;
    /**
     * 患者类型：	N-本地人；	O-外地人；	F-境外人员
     */
    private String patientType;
    /**
     * 科室编码
     */
    private String departmentCode;
    /**
     * 科室名称
     */
    private String departmentName;
    /**
     * 医生代码
     */
    private String doctorCode;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 住院号
     */
    private String hospitalizationNumber;
    /**
     * 就诊类型：	（门急诊）	100-普通门诊；	101-专科门诊；	102-专家门诊；	103-特需门诊；	104-专病门诊；	200-急诊；	600-体检；	999-其他；		（住院）	300-急诊观察；	400- 普通住院；	401- 特需住院；	500-家床；	999-其他；
     */
    private String treatmentType;
    /**
     * 病区
     */
    private String inpatientArea;
    /**
     * 床号
     */
    private String bedNumber;
    /**
     * 主诊断编码
     */
    private String mainDiagnosisCode;
    /**
     * 主诊断名称
     */
    private String mainDiagnosisName;
    /**
     * 其他诊断编码
     */
    private String otherDiagnosisCode;
    /**
     * 其他诊断名称
     */
    private String otherDiagnosisName;
    /**
     * 并发症代码
     */
    private String complicationCode;
    /**
     * 就医时间
     */
    private Date treatmentDatetime;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 现病史
     */
    private String currentMedicalHistory;
    /**
     * 既往史
     */
    private String pastMedicalHistory;
    /**
     * 过敏史
     */
    private String allergyHistory;
    /**
     * 遗传史
     */
    private String geneticHistory;
    /**
     * 住院时间
     */
    private Date inHospitalDatetime;
    /**
     * 出院时间
     */
    private Date leaveHospitalDatetime;
    /**
     * 出院方式：	1-医嘱离院；	2-医嘱转院；	3-医嘱转社区卫生服务机构/乡镇医院；	4-非医嘱离院；	5-死亡；	9-其他；
     */
    private String leaveHospitalStyle;
    /**
     * 出院状态：	1-治愈；	2-好转；	3-未愈；	4-死亡；	9-其他；
     */
    private String leaveHospitalState;
    /**
     * 出院诊断名称
     */
    private String leaveHospitalDiagnosisName;
    /**
     * 入院诊断名称
     */
    private String inHospitalDiagnosisName;
    /**
     * 出院描述
     */
    private String leaveHospitalDescription;
    /**
     * 电子票据号列表，英文逗号分隔
     */
    private String electronicBillIds;
    /**
     * 电子票据
     */
    private String electronicBill;
    /**
     * 区域编号
     */
    private Long areaId;
    /**
     * 医疗费用
     */
    private BigDecimal medicalCosts;
    /**
     * 住院津贴和住院看护津贴
     */
    private BigDecimal hospitalizationAllowance;
    /**
     * 骨折津贴
     */
    private BigDecimal diagnosisAllowance;
    /**
     * 建议理赔金额
     */
    private BigDecimal suggestCompensatingMoney;
    /**
     * 实际理赔医疗费用
     */
    private BigDecimal actualMedicalCosts;
    /**
     * 实际理赔住院津贴和住院看护津贴
     */
    private BigDecimal actualHospitalizationAllowance;
    /**
     * 实际理赔骨折津贴
     */
    private BigDecimal actualDiagnosisAllowance;
    /**
     * 实际理赔康复津贴
     */
    private BigDecimal actualRecoveryAllowance;
    /**
     * 实际理赔金额
     */
    private BigDecimal actualMoney;
    /**
     * 理赔时间
     */
    private Date compensatingDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态(0 待接单, 1 待盖章, 2 待处理, 3 待回访, 4 已完成)
     */
    private Integer status;

    /**
     * 批量变更前的原始状态，用于恢复机制
     */
    private Integer originalStatus;

    /**
     * 批量操作的唯一批次号，用于审计和追踪
     */
    private String batchOperationId;
    /**
     * 电子票据数据完整度(0 完整 1 不确定 2  无数据)
     */
    private Integer completeStatus;
    /**
     * 工单类型
     */
    private Integer type;
    /**
     * 补充资料
     */
    private String supplementaryFiles;
    /**
     * 补充资料记录标识
     */
    private Long supplementaryFileRecordId;
    
    /**
     * 理赔相关图片信息，JSON格式存储
     * 包含给付申请书和勘察报告的URL
     */
    private String claimImages;
}
