package cn.iocoder.yudao.module.insurance.service.workorder2;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.BatchRejectRespVO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderEventDo;
import cn.iocoder.yudao.module.insurance.dal.mysql.batchoperationlog.BatchOperationLogMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrder2Mapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrderEventMapper;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderStatusEnum;
import cn.iocoder.yudao.module.insurance.service.batchoperationlog.BatchOperationLogService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 批量拒绝工单功能测试
 *
 * <AUTHOR>
 */
@Import({WorkOrder2ServiceImpl.class})
public class BatchRejectWorkOrderTest extends BaseDbUnitTest {

    @Resource
    private WorkOrder2Service workOrder2Service;

    @Resource
    private WorkOrder2Mapper workOrder2Mapper;

    @Resource
    private WorkOrderEventMapper workOrderEventMapper;

    @Resource
    private BatchOperationLogMapper batchOperationLogMapper;

    @MockBean
    private BatchOperationLogService batchOperationLogService;

    @Test
    public void testBatchRejectWorkOrders_Success() {
        // 准备测试数据
        Date cutoffDate = new Date();
        Date treatmentDate = new Date(cutoffDate.getTime() - 24 * 60 * 60 * 1000); // 昨天
        
        // 创建待接单的工单
        WorkOrder2DO workOrder1 = createWorkOrder("123456789012345678", treatmentDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        WorkOrder2DO workOrder2 = createWorkOrder("123456789012345679", treatmentDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        
        // 创建不符合条件的工单（状态不是待接单）
        WorkOrder2DO workOrder3 = createWorkOrder("123456789012345680", treatmentDate, WorkOrderStatusEnum.WAIT_PROCESSING.getStatus());
        
        // 创建不符合条件的工单（日期超过截止日期）
        Date futureDate = new Date(cutoffDate.getTime() + 24 * 60 * 60 * 1000); // 明天
        WorkOrder2DO workOrder4 = createWorkOrder("123456789012345681", futureDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        
        workOrder2Mapper.insert(workOrder1);
        workOrder2Mapper.insert(workOrder2);
        workOrder2Mapper.insert(workOrder3);
        workOrder2Mapper.insert(workOrder4);

        // 执行批量拒绝操作
        BatchRejectRespVO result = workOrder2Service.batchRejectWorkOrders(cutoffDate);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getBatchId());
        assertEquals(2, result.getProcessedCount()); // 只有2个工单符合条件

        // 验证工单状态已更新
        WorkOrder2DO updatedWorkOrder1 = workOrder2Mapper.selectById(workOrder1.getId());
        WorkOrder2DO updatedWorkOrder2 = workOrder2Mapper.selectById(workOrder2.getId());
        WorkOrder2DO updatedWorkOrder3 = workOrder2Mapper.selectById(workOrder3.getId());
        WorkOrder2DO updatedWorkOrder4 = workOrder2Mapper.selectById(workOrder4.getId());

        // 符合条件的工单应该被更新
        assertEquals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(), updatedWorkOrder1.getStatus());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), updatedWorkOrder1.getOriginalStatus());
        assertEquals(result.getBatchId(), updatedWorkOrder1.getBatchOperationId());

        assertEquals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(), updatedWorkOrder2.getStatus());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), updatedWorkOrder2.getOriginalStatus());
        assertEquals(result.getBatchId(), updatedWorkOrder2.getBatchOperationId());

        // 不符合条件的工单应该保持不变
        assertEquals(WorkOrderStatusEnum.WAIT_PROCESSING.getStatus(), updatedWorkOrder3.getStatus());
        assertNull(updatedWorkOrder3.getOriginalStatus());
        assertNull(updatedWorkOrder3.getBatchOperationId());

        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), updatedWorkOrder4.getStatus());
        assertNull(updatedWorkOrder4.getOriginalStatus());
        assertNull(updatedWorkOrder4.getBatchOperationId());

        // 验证工单事件已创建
        List<WorkOrderEventDo> events1 = workOrderEventMapper.getWorkOrderEventList(workOrder1.getId());
        List<WorkOrderEventDo> events2 = workOrderEventMapper.getWorkOrderEventList(workOrder2.getId());
        
        assertTrue(events1.stream().anyMatch(e -> e.getType().equals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus())));
        assertTrue(events2.stream().anyMatch(e -> e.getType().equals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus())));
    }

    @Test
    public void testBatchRejectWorkOrders_NoMatchingWorkOrders() {
        // 准备测试数据 - 没有符合条件的工单
        Date cutoffDate = new Date();
        Date futureDate = new Date(cutoffDate.getTime() + 24 * 60 * 60 * 1000); // 明天
        
        // 创建不符合条件的工单
        WorkOrder2DO workOrder = createWorkOrder("123456789012345678", futureDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        workOrder2Mapper.insert(workOrder);

        // 执行批量拒绝操作
        BatchRejectRespVO result = workOrder2Service.batchRejectWorkOrders(cutoffDate);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getBatchId());
        assertEquals(0, result.getProcessedCount()); // 没有工单被处理

        // 验证工单状态未改变
        WorkOrder2DO updatedWorkOrder = workOrder2Mapper.selectById(workOrder.getId());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), updatedWorkOrder.getStatus());
        assertNull(updatedWorkOrder.getOriginalStatus());
        assertNull(updatedWorkOrder.getBatchOperationId());
    }

    private WorkOrder2DO createWorkOrder(String idCardNumber, Date treatmentDate, Integer status) {
        return WorkOrder2DO.builder()
                .hospitalCode("TEST001")
                .hospitalName("测试医院")
                .treatmentSerialNumberType(0)
                .treatmentSerialNumber("TEST" + System.currentTimeMillis())
                .name("测试用户")
                .idCardNumber(idCardNumber)
                .mobilePhoneNumber("13800138000")
                .treatmentDatetime(treatmentDate)
                .status(status)
                .build();
    }
}
