package cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog;

import lombok.*;

import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 批量操作日志 DO
 *
 * <AUTHOR>
 */
@TableName("insurance_batch_operation_log")
@KeySequence("insurance_batch_operation_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperationLogDO {

    /**
     * 唯一标识
     */
    @TableId
    private Long id;
    
    /**
     * 唯一批次号
     */
    private String batchId;
    
    /**
     * 操作类型 (例如: BATCH_REJECT, BATCH_RECOVER)
     */
    private String operationType;
    
    /**
     * 操作状态 (例如: COMPLETED, FAILED)
     */
    private String status;
    
    /**
     * 操作执行时的参数 (例如: { "cutoffDate": "2024-12-31" })
     */
    private String parameters;
    
    /**
     * 成功处理的工单数量
     */
    private Integer processedCount;
    
    /**
     * 操作员ID
     */
    private String operatorId;
    
    /**
     * 操作员姓名
     */
    private String operatorName;
    
    /**
     * 操作开始时间
     */
    private Date startTime;
    
    /**
     * 操作结束时间
     */
    private Date endTime;
    
    /**
     * 备注 (例如: 记录错误信息)
     */
    private String remarks;
}
