package cn.iocoder.yudao.module.insurance.service.workorder2;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.*;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.*;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderEventDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderPdfDo;
import cn.iocoder.yudao.module.insurance.enums.workorder.InsuranceTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.SupplementaryFileType;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderStatusEnum;

/**
 * 工单 Service 接口
 *
 * <AUTHOR>
 */
public interface WorkOrder2Service {

    /**
     * 创建工单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWorkOrder2(@Valid WorkOrder2CreateReqVO createReqVO);

    /**
     * 更新工单
     *
     * @param updateReqVO 更新信息
     */
    void updateWorkOrder2(@Valid WorkOrder2UpdateReqVO updateReqVO);

    /**
     * 删除工单
     *
     * @param id 编号
     */
    void deleteWorkOrder2(Long id);

    /**
     * 获得工单
     *
     * @param id 编号
     * @return 工单
     */
    WorkOrder2DO getWorkOrder2(Long id);

    /**
     * 获得工单pdf
     *
     * @param orderId 工单编号
     * @return 工单pdf
     */
    WorkOrderPdfDo getWorkOrderPdf(Long orderId);

    Map<Long, WorkOrderPdfDo> getWorkOrderPdfMap(Collection<Long> orderIds);

    /**
     * 获得工单列表
     *
     * @param ids 编号
     * @return 工单列表
     */
    List<WorkOrder2DO> getWorkOrder2List(Collection<Long> ids);

    /**
     * 获得工单分页
     *
     * @param pageReqVO 分页查询
     * @return 工单分页
     */
    PageResult<WorkOrder2DO> getWorkOrder2Page(WorkOrder2PageReqVO pageReqVO);

    /**
     * 获得工单列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 工单列表
     */
    List<WorkOrder2DO> getWorkOrder2List(WorkOrder2ExportReqVO exportReqVO);

    List<WorkOrder2ExcelVO> getWorkOrder2List2(WorkOrder2ExportReqVO exportReqVO);

    /**
     * 接单
     *
     * @param id 编号
     */
    void takeWorkOrder(Long id);

    /**
     * 盖章
     *
     * @param id 编号
     */
    void hospitalCheck(Long id);

    /**
     * 处理
     *
     * @param id 编号
     */
    void process(Long id);

    /**
     * 回访
     *
     * @param updateInsuranceVo
     */
    void visit(WorkOrderUpdateInsuranceVo updateInsuranceVo);

    /**
     * 获得工单分页
     *
     * @param openApiPageReqVO 分页查询
     * @return 工单分页
     */
    PageResult<WorkOrder2DO> getWorkOrder2Page(WorkOrder2OpenApiPageReqVO openApiPageReqVO);

    /**
     * 获得工单事件列表
     *
     * @param workOrderId 工单ID
     * @return 工单事件列表
     */
    List<WorkOrderEventDo> getWorkOrderEventList(Long workOrderId);

    /**
     * 拒绝
     *
     * @param updateInsuranceVo
     */
    void rejectWorkOrder(WorkOrderUpdateInsuranceVo updateInsuranceVo);

    /**
     * 延后
     *
     * @param updateInsuranceVo
     */
    void delayWorkOrder(WorkOrderUpdateInsuranceVo updateInsuranceVo);

    void compensatingReport(WorkOrderUpdateInsuranceVo workOrderUpdateInsuranceVo);

    String buildInsuancePolicy(String idCard, String phone, String address);

    Long createWorkOrderDo(WorkOrder2DO workOrder);

    String buildUnsignedPdf(WorkOrder2DO workOrder2DO);

    Long count();

    Long countWaitAcceptOrder();

    Long countWaitHandle();

    Long countWaitVisiting();

    Long countFinished();

    Long countSumAcceptOrder();

    BigDecimal countCompensatingMoney();

    List<InsuranceCompensatingMoney> countCompensatingMoneyGroupByCompany();

    List<CompensatingMonthPersonNumber> countCompensatingPersonNumberGroupByMonth(Integer year);

    List<CompensatingAreaPersonNumber> countCompensatingPersonNumberGroupByArea(Integer year);

    PageResult<WorkOrder2RespVO> getWorkOrder2Page2(WorkOrder2PageReqVO pageVO);

    PageResult<CompensatingPersonAndCompany> getCompensatingPersonAndCompanyPage(WorkOrder2PageReqVO pageReqVO);

    Long countNoDataOrder();

    Long countUnsureOrder();

    Long countCompleteOrder();

    BigDecimal countSuggestCompensatingMoney();

    List<WorkOrderStatisticsByArea> countWorkOrderGroupByArea();

    WorkOrderStatisticsByAge countWorkOrderGroupByAge(Integer beginAge, Integer endAge);

    void returnToPrev(WorkOrderUpdateInsuranceVo vo);

    WorkOrderStatisticsByDiagnosisCodeAndAge countWorkOrderByDiagnosisCodeAndAge(String diagnosisCode, Integer beginAge, Integer endAge);

    List<WorkOrderStatisticsByDiagnosisCodeAndArea> countWorkOrderByDiagnosisCodeGroupByArea(String diagnosisCode);

    List<WorkOrderStatisticsByDiagnosisCodeAndMonth> countWorkOrderByDiagnosisCodeGroupByMonth(String diagnosisCode, Integer year);

    List<WorkOrderStatisticsByDiagnosisCodeAndSex> countWorkOrderByDiagnosisCodeGroupBySex(String diagnosisCode);

    List<TopDiagnosis> countTopDiagnosis(Integer top);

    void confirm(WorkOrderUpdateInsuranceVo workOrderUpdateInsuranceVo);

    Long countRejected();

    Long countDelay();

    Long countWorkOrder(List<InsuranceTypeEnum> types, WorkOrderStatusEnum workOrderStatusEnum);

    List<String> getHospitalNameList();

    List<SettlementWorkOrderImportResultVo> importSettlementWorkOrder(List<SettlementWorkOrderImportVo> workOrder2ImportExportExcelVOList);

    void createPdf(Long id);

    List<WorkOrder2DO> getWorkOrder2ListWithoutElecTicket();

    void updateWorkOrder2(WorkOrder2DO workOrder2DO);

    List<WorkOrderStatRespVo> getWorkOrderStat(WorkOrderStatReqVo reqVo);

    /**
     * 更新工单补充资料
     *
     * @param workOrderId 工单ID
     * @param fileUrls 文件URL列表
     */
    void addWorkOrderSupplementaryInfo(Long workOrderId, List<String> fileUrls);

    void addWorkOrderSupplementaryInfo(Long workOrderId, SupplementaryFileType type, String url);

    void delWorkOrderSupplementaryInfo(Long workOrderId, SupplementaryFileType type, String fileUrl);

    List<ClaimAmountImportResultVO> importClaimAmountWorkOrder(List<ClaimAmountImportVO> claimAmountImportVOList);

    List<WandaDataImportResultVO> importWandaData(List<WandaDataImportVO> dataList);

    /**
     * 获取医院数据量统计
     * 
     * @return 医院数据量统计列表
     */
    List<HospitalDataStatisticsVO> getHospitalDataStatistics();

    /**
     * 获取医院详细统计
     * 
     * @param hospitalName 医院名称
     * @return 医院详细统计
     */
    HospitalDetailStatisticsVO getHospitalDetailStatistics(String hospitalName);

    /**
     * 检查是否存在工单
     *
     * @return 是否存在
     */
    boolean existsWorkOrder(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType);

    /**
     * 根据关键信息获取工单
     *
     * @param idCardNumber 身份证号
     * @param hospitalCode 医院代码
     * @param treatmentSerialNumber 就诊流水号
     * @param treatmentSerialNumberType 门急诊类型
     * @return 工单
     */
    WorkOrder2DO getWorkOrderByKey(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType);

    /**
     * 根据关键信息和险种类型获取工单
     *
     * @param idCardNumber 身份证号
     * @param hospitalCode 医院代码
     * @param treatmentSerialNumber 就诊流水号
     * @param treatmentSerialNumberType 门急诊类型
     * @param type 险种类型
     * @return 工单
     */
    WorkOrder2DO getWorkOrderByKeyAndType(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType, Integer type);

    WorkOrder2DO getWorkOrder2BySupplementaryFileRecordId(Long supplementaryFileRecordId);

    // Authorization-aware count methods
    Long countWithAuth();

    Long countWaitAcceptOrderWithAuth();

    Long countWaitHandleWithAuth();

    Long countWaitVisitingWithAuth();

    Long countFinishedWithAuth();

    Long countRejectedWithAuth();

    Long countDelayWithAuth();

    Long countSumAcceptOrderWithAuth();

    Long countNoDataOrderWithAuth();

    Long countUnsureOrderWithAuth();

    Long countCompleteOrderWithAuth();

    Long countWorkOrderWithAuth(List<InsuranceTypeEnum> types, WorkOrderStatusEnum workOrderStatusEnum);

    List<String> getHospitalNameListWithAuth();

    /**
     * 批量拒绝工单
     *
     * @param cutoffDate 截止日期
     * @return 批量操作结果，包含批次号和处理数量
     */
    BatchRejectRespVO batchRejectWorkOrders(java.util.Date cutoffDate);

    /**
     * 批量恢复工单
     *
     * @param batchOperationId 批次操作ID
     * @return 批量恢复结果，包含批次号和处理数量
     */
    BatchRecoverRespVO batchRecoverWorkOrders(String batchOperationId);
}
