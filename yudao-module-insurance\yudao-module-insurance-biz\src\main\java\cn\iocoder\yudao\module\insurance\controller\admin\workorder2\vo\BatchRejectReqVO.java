package cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 批量拒绝工单请求 VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 批量拒绝工单请求 VO")
@Data
public class BatchRejectReqVO {

    @ApiModelProperty(value = "截止日期", required = true, notes = "只有就医时间小于或等于此日期的待接单工单会被批量拒绝")
    @NotNull(message = "截止日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date cutoffDate;
}
