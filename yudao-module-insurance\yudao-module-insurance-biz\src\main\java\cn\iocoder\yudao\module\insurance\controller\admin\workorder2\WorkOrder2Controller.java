package cn.iocoder.yudao.module.insurance.controller.admin.workorder2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.valid.Reject;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.valid.Visit;
import cn.iocoder.yudao.module.insurance.convert.workorder2.WorkOrder2ConvertImpl;
import cn.iocoder.yudao.module.insurance.dal.dataobject.bankcard.BankCardDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderEventDo;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderPdfDo;
import cn.iocoder.yudao.module.insurance.service.bankcard.BankCardService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.*;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.convert.workorder2.WorkOrder2Convert;
import cn.iocoder.yudao.module.insurance.service.workorder2.WorkOrder2Service;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "管理后台 - 工单")
@RestController
@RequestMapping("/insurance/work-order")
@Validated
public class WorkOrder2Controller {

    @Resource
    private WorkOrder2Service workOrder2Service;

    @Resource
    private BankCardService bankCardService;

    @Resource
    private FileApi fileApi;

    @Resource
    private AdminUserApi adminUserApi;

    @PostMapping("/create")
    @ApiOperation("创建工单")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:create')")
    public CommonResult<Long> createWorkOrder2(@Valid @RequestBody WorkOrder2CreateReqVO createReqVO) {
        return success(workOrder2Service.createWorkOrder2(createReqVO));
    }

    @PutMapping("/update")
    @ApiOperation("更新工单")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:update')")
    public CommonResult<Boolean> updateWorkOrder2(@Valid @RequestBody WorkOrder2UpdateReqVO updateReqVO) {
        workOrder2Service.updateWorkOrder2(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除工单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('insurance:work-order:delete')")
    public CommonResult<Boolean> deleteWorkOrder2(@RequestParam("id") Long id) {
        workOrder2Service.deleteWorkOrder2(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得工单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('insurance:work-order:query')")
    public CommonResult<WorkOrder2RespVO> getWorkOrder2(@RequestParam("id") Long id) {
        WorkOrder2DO workOrder2 = workOrder2Service.getWorkOrder2(id);
        WorkOrder2RespVO vo = WorkOrder2Convert.INSTANCE.convert(workOrder2);
        vo.setPdf(WorkOrder2Convert.INSTANCE.convertPdf(workOrder2Service.getWorkOrderPdf(id)));
        if (vo.getPdf() == null) {
            workOrder2Service.buildUnsignedPdf(workOrder2);
            workOrder2Service.buildInsuancePolicy(workOrder2.getIdCardNumber(), workOrder2.getMobilePhoneNumber(), workOrder2.getAddress());
            vo.setPdf(WorkOrder2Convert.INSTANCE.convertPdf(workOrder2Service.getWorkOrderPdf(id)));
        }
        List<WorkOrderEventDo> events = workOrder2Service.getWorkOrderEventList(id);
        WorkOrderEventDo firstEvent = new WorkOrderEventDo();
        firstEvent.setType(0);
        firstEvent.setCreateTime(workOrder2.getCreateTime());
        events.add(0, firstEvent);
        vo.setEvents(processEventList(events));
        return success(vo);
    }

    @GetMapping("/list")
    @ApiOperation("获得工单列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('insurance:work-order:query')")
    public CommonResult<List<WorkOrder2RespVO>> getWorkOrder2List(@RequestParam("ids") Collection<Long> ids) {
        List<WorkOrder2DO> list = workOrder2Service.getWorkOrder2List(ids);
        return success(WorkOrder2Convert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得工单分页")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:query')")
    public CommonResult<PageResult<WorkOrder2RespVO>> getWorkOrder2Page(@Valid WorkOrder2PageReqVO pageVO) {
        PageResult<WorkOrder2DO> pageResult = workOrder2Service.getWorkOrder2Page(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal()));
        }

        Collection<Long> orderIds = CollectionUtils.convertList(pageResult.getList(), WorkOrder2DO::getId);
        Collection<String> idCardNumbers = CollectionUtils.convertList(pageResult.getList(), WorkOrder2DO::getIdCardNumber);
        Map<Long, WorkOrderPdfDo> pdfMap = workOrder2Service.getWorkOrderPdfMap(orderIds);
        Map<String, List<BankCardDO>> bankCardMap = bankCardService.getBankCardMap(idCardNumbers);
        List<WorkOrder2RespVO> orderList = new ArrayList<>(pageResult.getList().size());
        pageResult.getList().forEach(order -> {
            WorkOrder2RespVO respVO = WorkOrder2Convert.INSTANCE.convert(order);
            respVO.setPdf(WorkOrder2Convert.INSTANCE.convertPdf(pdfMap.get(order.getId())));
            respVO.setBankCardList(bankCardMap.get(order.getIdCardNumber()));
            orderList.add(respVO);
        });
        return success(WorkOrder2Convert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/page2")
    @ApiOperation("获得工单分页")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:query')")
    public CommonResult<PageResult<WorkOrder2RespVO>> getWorkOrder2Page2(@Valid WorkOrder2PageReqVO pageVO) {
        PageResult<WorkOrder2RespVO> pageResult = workOrder2Service.getWorkOrder2Page2(pageVO);
        pageResult.getList().forEach(v -> {
            v.setDesensitizedName(DesensitizedUtil.chineseName(v.getName()));
            v.setDesensitizedIdCardNumber(DesensitizedUtil.idCardNum(v.getIdCardNumber(), 5, 2));
            v.setDesensitizedMobilePhoneNumber(DesensitizedUtil.mobilePhone(v.getMobilePhoneNumber()));
        });
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出工单 Excel")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:export')")
    @OperateLog(type = EXPORT)
    public void exportWorkOrder2Excel(@Valid WorkOrder2ExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<WorkOrder2ExcelVO> datas = workOrder2Service.getWorkOrder2List2(exportReqVO);
        ExcelUtils.write(response, "工单.xls", "数据", WorkOrder2ExcelVO.class, datas);
    }

    @PutMapping("/take")
    @ApiOperation("接单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('insurance:work-order:take')")
    public CommonResult<Boolean> takeWorkOrder(@RequestParam("id") Long id) {
        workOrder2Service.takeWorkOrder(id);
        return success(true);
    }

    @PutMapping("/reject")
    @ApiOperation("拒绝")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:reject')")
    public CommonResult<Boolean> rejectWorkOrder(@Validated(Reject.class) @RequestBody WorkOrderUpdateInsuranceVo vo) {
        workOrder2Service.rejectWorkOrder(vo);
        return success(true);
    }

    @PutMapping("/delay")
    @ApiOperation("延后")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:delay')")
    public CommonResult<Boolean> delayWorkOrder(@Valid @RequestBody WorkOrderUpdateInsuranceVo vo) {
        workOrder2Service.delayWorkOrder(vo);
        return success(true);
    }

    @PutMapping("/hospital-check")
    @ApiOperation("盖章")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('insurance:work-order:hospital-check')")
    public CommonResult<Boolean> hospitalCheck(@RequestParam("id") Long id) {
        workOrder2Service.hospitalCheck(id);
        return success(true);
    }

    @PutMapping("/process")
    @ApiOperation("处理")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('insurance:work-order:process')")
    public CommonResult<Boolean> process(@RequestParam("id") Long id) {
        workOrder2Service.process(id);
        return success(true);
    }

    @PutMapping("/visit")
    @ApiOperation("回访")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:visit')")
    public CommonResult<Boolean> visit(@Validated(Visit.class) @RequestBody WorkOrderUpdateInsuranceVo vo) {
        workOrder2Service.visit(vo);
        return success(true);
    }

    @PutMapping("/return")
    @ApiOperation("回退")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:return')")
    public CommonResult<Boolean> returnToPrev(@Valid @RequestBody WorkOrderUpdateInsuranceVo vo) {
        workOrder2Service.returnToPrev(vo);
        return success(true);
    }

    @GetMapping("/hospitalNames")
    @ApiOperation("获得医院名称列表")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:query')")
    public CommonResult<List<String>> getHospitalNameList() {
        return success(workOrder2Service.getHospitalNameList());
    }

    @PostMapping("/import-settlement-work-order")
    @ApiOperation("导入已赔付工单")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:settlement-import')")
    @OperateLog(enable = false)
    public CommonResult<String> importSettlementWorkOrder(@RequestParam("file") MultipartFile file) throws IOException {
        List<SettlementWorkOrderImportVo> workOrder2ImportExportExcelVOList = ExcelUtils.read(file, SettlementWorkOrderImportVo.class);
        List<SettlementWorkOrderImportResultVo> settlementWorkOrderImportResultVos = workOrder2Service.importSettlementWorkOrder(workOrder2ImportExportExcelVOList);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(1024*30);
        EasyExcel.write(byteArrayOutputStream, SettlementWorkOrderImportResultVo.class)
                .autoCloseStream(true)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                .sheet("数据").doWrite(settlementWorkOrderImportResultVos);
        String filePath = fileApi.createFile(String.format("已赔付工单导入结果(%s)%d.xls", FileNameUtil.mainName(file.getOriginalFilename()), new Date().getTime()), byteArrayOutputStream.toByteArray());
        return success(filePath);
    }

    @PostMapping("/import-claim-amount-work-order")
    @ApiOperation("导入已理赔工单")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:claim-import')")
    @OperateLog(enable = false)
    public CommonResult<String> importClaimAmountWorkOrder(@RequestParam("file") MultipartFile file) throws IOException {
        // 读取 Excel 文件并解析为 ClaimAmountImportVO 列表
        List<ClaimAmountImportVO> claimAmountImportVOList = ExcelUtils.read(file, ClaimAmountImportVO.class);
        
        // 调用服务层处理导入逻辑，返回结果列表
        List<ClaimAmountImportResultVO> importResultList = workOrder2Service.importClaimAmountWorkOrder(claimAmountImportVOList);
        
        // 将结果写入 Excel 文件
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(1024 * 30);
        EasyExcel.write(byteArrayOutputStream, ClaimAmountImportResultVO.class)
                .autoCloseStream(true)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动适配列宽
                .sheet("数据").doWrite(importResultList);
        
        // 生成结果文件并返回文件路径
        String filePath = fileApi.createFile(
            String.format("已理赔工单导入结果(%s)%d.xls", FileNameUtil.mainName(file.getOriginalFilename()), new Date().getTime()),
            byteArrayOutputStream.toByteArray()
        );
        return success(filePath);
    }

    @PutMapping("/createPdf")
    @ApiOperation("创建未签章pdf")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:update')")
    public CommonResult<Boolean> createPdf(@RequestParam("id") Long id) {
        workOrder2Service.createPdf(id);
        return success(true);
    }

    @GetMapping("/stat")
    @ApiOperation("获取保险公司理赔统计数据")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:query')")
    public CommonResult<List<WorkOrderStatRespVo>> getWorkOrderStat(@Valid WorkOrderStatReqVo reqVo) {
        List<WorkOrderStatRespVo> result = workOrder2Service.getWorkOrderStat(reqVo);
        return success(result);
    }

    @PostMapping("/import-wanda-data")
    @ApiOperation("导入万达数据")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:import')")
    public CommonResult<String> importWandaData(@RequestParam("file") MultipartFile file) throws IOException {
        List<WandaDataImportVO> dataList = ExcelUtils.read(file, WandaDataImportVO.class);
        List<WandaDataImportResultVO> resultList = workOrder2Service.importWandaData(dataList);
        
        // 写入Excel结果文件
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(1024 * 30);
        EasyExcel.write(byteArrayOutputStream, WandaDataImportResultVO.class)
                .autoCloseStream(true)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动适配列宽
                .sheet("数据").doWrite(resultList);
        
        // 生成结果文件并返回文件路径
        String filePath = fileApi.createFile(
            String.format("万达数据导入结果(%s)%d.xls", FileNameUtil.mainName(file.getOriginalFilename()), new Date().getTime()),
            byteArrayOutputStream.toByteArray()
        );
        return success(filePath);
    }

    @PostMapping("/batch/reject")
    @ApiOperation("批量拒绝工单")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:batch-reject')")
    @OperateLog(type = UPDATE)
    public CommonResult<BatchRejectRespVO> batchRejectWorkOrders(@Valid @RequestBody BatchRejectReqVO reqVO) {
        BatchRejectRespVO respVO = workOrder2Service.batchRejectWorkOrders(reqVO.getCutoffDate());
        return success(respVO);
    }

    @PostMapping("/batch/recover")
    @ApiOperation("批量恢复工单")
    @PreAuthorize("@ss.hasPermission('insurance:work-order:batch-recover')")
    @OperateLog(type = UPDATE)
    public CommonResult<BatchRecoverRespVO> batchRecoverWorkOrders(@Valid @RequestBody BatchRecoverReqVO reqVO) {
        BatchRecoverRespVO respVO = workOrder2Service.batchRecoverWorkOrders(reqVO.getBatchOperationId());
        return success(respVO);
    }

    /**
     * 处理工单事件列表，并附加操作人信息
     * 
     * @param events 工单事件列表
     * @return 附加了操作人信息的工单事件VO列表
     */
    private List<WorkOrder2RespVO.WorkOrderEvent> processEventList(List<WorkOrderEventDo> events) {
        if (events == null || events.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 1. 转换为 WorkOrderEvent 列表
        List<WorkOrder2RespVO.WorkOrderEvent> eventVOs = WorkOrder2Convert.INSTANCE.convertEventList(events);
        
        // 2. 提取所有创建者ID集合
        Set<Long> creatorIds = events.stream()
                .map(event -> {
                    try {
                        String creator = event.getCreator();
                        if (creator != null && !creator.isEmpty()) {
                            return Long.parseLong(creator);
                        }
                    } catch (NumberFormatException ignored) {
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 3. 如果没有创建者，直接返回
        if (creatorIds.isEmpty()) {
            return eventVOs;
        }
        
        // 4. 批量获取用户信息
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(creatorIds);
        
        // 5. 设置用户昵称
        for (int i = 0; i < events.size(); i++) {
            WorkOrderEventDo eventDo = events.get(i);
            WorkOrder2RespVO.WorkOrderEvent eventVO = eventVOs.get(i);
            
            try {
                String creator = eventDo.getCreator();
                if (creator != null && !creator.isEmpty()) {
                    Long creatorId = Long.parseLong(creator);
                    AdminUserRespDTO user = userMap.get(creatorId);
                    if (user != null) {
                        eventVO.setCreatorNickname(user.getNickname());
                    }
                }
            } catch (NumberFormatException ignored) {
            }
        }
        
        return eventVOs;
    }
}
