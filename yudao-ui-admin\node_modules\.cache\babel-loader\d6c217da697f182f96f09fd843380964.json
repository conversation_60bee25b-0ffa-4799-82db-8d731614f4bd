{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\eslint-loader@2.2.1_eslint@7.15.0_webpack@4.46.0\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\api\\insurance\\workOrder.js", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\api\\insurance\\workOrder.js", "mtime": 1754285376826}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\eslint-loader@2.2.1_eslint@7.15.0_webpack@4.46.0\\node_modules\\eslint-loader\\index.js", "mtime": 1667694382021}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createWorkOrder", "data", "request", "url", "method", "updateWorkOrder", "deleteWorkOrder", "id", "getWorkOrder", "getWorkOrderPage", "query", "params", "exportWorkOrderExcel", "responseType", "takeWorkOrder", "hospitalCheck", "process", "visit", "reject", "delay", "returnWorkOrder", "getHospitalNames", "createPdf", "getWorkOrderStat", "getBatchOperationLogPage"], "sources": ["C:/projects/shenlan/insurance/yudao-ui-admin/src/api/insurance/workOrder.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 创建工单\r\nexport function createWorkOrder(data) {\r\n  return request({\r\n    url: '/insurance/work-order/create',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 更新工单\r\nexport function updateWorkOrder(data) {\r\n  return request({\r\n    url: '/insurance/work-order/update',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除工单\r\nexport function deleteWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/work-order/delete?id=' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获得工单\r\nexport function getWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/work-order/get?id=' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获得工单分页\r\nexport function getWorkOrderPage(query) {\r\n  return request({\r\n    url: '/insurance/work-order/page2',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 导出工单 Excel\r\nexport function exportWorkOrderExcel(query) {\r\n  return request({\r\n    url: '/insurance/work-order/export-excel',\r\n    method: 'get',\r\n    params: query,\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n// 接单\r\nexport function takeWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/work-order/take?id=' + id,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 盖章\r\nexport function hospitalCheck(id) {\r\n  return request({\r\n    url: '/insurance/work-order/hospital-check?id=' + id,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 处理\r\nexport function process(id) {\r\n  return request({\r\n    url: '/insurance/work-order/process?id=' + id,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 回访\r\nexport function visit(data) {\r\n  return request({\r\n    url: '/insurance/work-order/visit',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 拒绝\r\nexport function reject(data) {\r\n  return request({\r\n    url: '/insurance/work-order/reject',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 延后\r\nexport function delay(data) {\r\n  return request({\r\n    url: '/insurance/work-order/delay',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 回退\r\nexport function returnWorkOrder(id) {\r\n  return request({\r\n    url: '/insurance/work-order/return',\r\n    method: 'put',\r\n    data: {id}\r\n  })\r\n}\r\n\r\nexport function getHospitalNames() {\r\n  return request({\r\n    url: '/insurance/work-order/hospitalNames',\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n//创建未签章pdf\r\nexport function createPdf(id) {\r\n  return request({\r\n    url: '/insurance/work-order/createPdf?id=' + id,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n//获取保险公司理赔统计数据\r\nexport function getWorkOrderStat(query) {\r\n  return request({\r\n    url: '/insurance/work-order/stat',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获得批量操作日志分页\r\nexport function getBatchOperationLogPage(query) {\r\n  return request({\r\n    url: '/insurance/work-order/batch-operation-logs/page',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACO,SAASA,eAAT,CAAyBC,IAAzB,EAA+B;EACpC,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,8BADQ;IAEbC,MAAM,EAAE,MAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASI,eAAT,CAAyBJ,IAAzB,EAA+B;EACpC,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,8BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASK,eAAT,CAAyBC,EAAzB,EAA6B;EAClC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,qCAAqCI,EAD7B;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASI,YAAT,CAAsBD,EAAtB,EAA0B;EAC/B,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,kCAAkCI,EAD1B;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASK,gBAAT,CAA0BC,KAA1B,EAAiC;EACtC,OAAO,IAAAR,gBAAA,EAAQ;IACbC,GAAG,EAAE,6BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbO,MAAM,EAAED;EAHK,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASE,oBAAT,CAA8BF,KAA9B,EAAqC;EAC1C,OAAO,IAAAR,gBAAA,EAAQ;IACbC,GAAG,EAAE,oCADQ;IAEbC,MAAM,EAAE,KAFK;IAGbO,MAAM,EAAED,KAHK;IAIbG,YAAY,EAAE;EAJD,CAAR,CAAP;AAMD,C,CAED;;;AACO,SAASC,aAAT,CAAuBP,EAAvB,EAA2B;EAChC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,mCAAmCI,EAD3B;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASW,aAAT,CAAuBR,EAAvB,EAA2B;EAChC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,6CAA6CI,EADrC;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASY,OAAT,CAAiBT,EAAjB,EAAqB;EAC1B,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,sCAAsCI,EAD9B;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASa,KAAT,CAAehB,IAAf,EAAqB;EAC1B,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,6BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASiB,MAAT,CAAgBjB,IAAhB,EAAsB;EAC3B,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,8BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASkB,KAAT,CAAelB,IAAf,EAAqB;EAC1B,OAAO,IAAAC,gBAAA,EAAQ;IACbC,GAAG,EAAE,6BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbH,IAAI,EAAEA;EAHO,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASmB,eAAT,CAAyBb,EAAzB,EAA6B;EAClC,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,8BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbH,IAAI,EAAE;MAACM,EAAE,EAAFA;IAAD;EAHO,CAAR,CAAP;AAKD;;AAEM,SAASc,gBAAT,GAA4B;EACjC,OAAO,IAAAnB,gBAAA,EAAQ;IACbC,GAAG,EAAE,qCADQ;IAEbC,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASkB,SAAT,CAAmBf,EAAnB,EAAuB;EAC5B,OAAO,IAAAL,gBAAA,EAAQ;IACbC,GAAG,EAAE,wCAAwCI,EADhC;IAEbH,MAAM,EAAE;EAFK,CAAR,CAAP;AAID,C,CAED;;;AACO,SAASmB,gBAAT,CAA0Bb,KAA1B,EAAiC;EACtC,OAAO,IAAAR,gBAAA,EAAQ;IACbC,GAAG,EAAE,4BADQ;IAEbC,MAAM,EAAE,KAFK;IAGbO,MAAM,EAAED;EAHK,CAAR,CAAP;AAKD,C,CAED;;;AACO,SAASc,wBAAT,CAAkCd,KAAlC,EAAyC;EAC9C,OAAO,IAAAR,gBAAA,EAAQ;IACbC,GAAG,EAAE,iDADQ;IAEbC,MAAM,EAAE,KAFK;IAGbO,MAAM,EAAED;EAHK,CAAR,CAAP;AAKD"}]}