package cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 批量恢复工单请求 VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 批量恢复工单请求 VO")
@Data
public class BatchRecoverReqVO {

    @ApiModelProperty(value = "批次操作ID", required = true, notes = "要恢复的批量操作的唯一标识")
    @NotBlank(message = "批次操作ID不能为空")
    private String batchOperationId;
}
