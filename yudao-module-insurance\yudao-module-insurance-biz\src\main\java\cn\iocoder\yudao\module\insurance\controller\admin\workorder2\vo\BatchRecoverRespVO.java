package cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量恢复工单响应 VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 批量恢复工单响应 VO")
@Data
public class BatchRecoverRespVO {

    @ApiModelProperty(value = "被恢复的批次号", required = true, notes = "被恢复的批量操作的唯一标识")
    private String batchId;

    @ApiModelProperty(value = "处理数量", required = true, notes = "本次恢复操作实际影响的工单数量")
    private Integer processedCount;
}
