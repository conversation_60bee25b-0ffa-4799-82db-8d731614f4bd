package cn.iocoder.yudao.module.insurance.enums.workorder;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WorkOrderStatusEnum {
    WAIT_TAKING(0, "待接单"),
    WAIT_HOSPITAL_CHECK(1, "待盖章"),
    WAIT_PROCESSING(2, "待处理"),
    WAIT_VISITING(3, "待回访"),
    FINISHED(4, "已完成"),
    REJECT(5, "已拒绝"),
    DELAY(6, "已延后"),
    ADMINISTRATIVELY_REJECTED(7, "行政拒绝");
    /**
     * 状态
     */
    private final Integer status;
    /**
     * 资源类型名
     */
    private final String name;
}
