# 故事 1.1 操作手册：添加"行政拒绝"状态

## 概述

本文档指导系统管理员如何在后台字典管理中添加新的"行政拒绝"状态，以支持批量操作功能。

## 前提条件

- 已完成数据库迁移脚本的执行
- 已部署包含新枚举值的后端代码
- 具有系统管理员权限

## 操作步骤

### 1. 登录系统后台

1. 使用管理员账号登录系统后台
2. 导航到"系统管理" -> "字典管理"

### 2. 查找工单状态字典

1. 在字典类型列表中查找"工单状态"相关的字典类型
2. 通常字典类型编码为 `work_order_status` 或类似名称

### 3. 添加新的字典数据

1. 点击对应字典类型的"字典数据"按钮
2. 点击"新增"按钮
3. 填写以下信息：
   - **字典标签**: 行政拒绝
   - **字典键值**: 7
   - **字典排序**: 7
   - **状态**: 正常
   - **备注**: 批量操作中的行政拒绝状态

### 4. 保存并验证

1. 点击"确定"保存新的字典数据
2. 刷新页面确认新状态已添加成功
3. 在工单管理页面验证新状态是否可用

## 注意事项

- 字典键值必须为 `7`，与代码中的枚举值保持一致
- 确保字典标签为"行政拒绝"，与枚举中的名称一致
- 如果系统中已存在键值为 7 的其他状态，请联系开发人员处理

## 验证方法

1. 在工单列表页面，状态筛选下拉框中应包含"行政拒绝"选项
2. 在工单详情页面，状态显示应正确展示"行政拒绝"
3. 批量操作功能应能正常使用新状态

## 故障排除

如果遇到以下问题：

1. **新状态不显示**: 检查字典键值是否为 7，字典标签是否正确
2. **系统报错**: 确认数据库迁移脚本已正确执行
3. **权限问题**: 确认当前用户具有字典管理权限

## 联系支持

如果在操作过程中遇到问题，请联系技术支持团队。
