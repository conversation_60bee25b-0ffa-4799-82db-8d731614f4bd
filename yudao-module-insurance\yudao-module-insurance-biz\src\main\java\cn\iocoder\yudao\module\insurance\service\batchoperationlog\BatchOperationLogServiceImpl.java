package cn.iocoder.yudao.module.insurance.service.batchoperationlog;

import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;
import cn.iocoder.yudao.module.insurance.dal.mysql.batchoperationlog.BatchOperationLogMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 批量操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class BatchOperationLogServiceImpl implements BatchOperationLogService {

    @Resource
    private BatchOperationLogMapper batchOperationLogMapper;

    @Override
    public Long createBatchOperationLog(BatchOperationLogDO batchOperationLog) {
        batchOperationLogMapper.insert(batchOperationLog);
        return batchOperationLog.getId();
    }

    @Override
    public void updateBatchOperationLog(BatchOperationLogDO batchOperationLog) {
        batchOperationLogMapper.updateById(batchOperationLog);
    }

    @Override
    public BatchOperationLogDO getBatchOperationLogByBatchId(String batchId) {
        return batchOperationLogMapper.selectByBatchId(batchId);
    }

    @Override
    public BatchOperationLogDO getBatchOperationLog(Long id) {
        return batchOperationLogMapper.selectById(id);
    }
}
