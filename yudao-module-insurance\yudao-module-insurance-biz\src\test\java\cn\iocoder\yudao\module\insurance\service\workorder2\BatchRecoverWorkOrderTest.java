package cn.iocoder.yudao.module.insurance.service.workorder2;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.BatchRecoverRespVO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderEventDo;
import cn.iocoder.yudao.module.insurance.dal.mysql.batchoperationlog.BatchOperationLogMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrder2Mapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrderEventMapper;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationStatusEnum;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderStatusEnum;
import cn.iocoder.yudao.module.insurance.service.batchoperationlog.BatchOperationLogService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 批量恢复工单功能测试
 *
 * <AUTHOR>
 */
@Import({WorkOrder2ServiceImpl.class})
public class BatchRecoverWorkOrderTest extends BaseDbUnitTest {

    @Resource
    private WorkOrder2Service workOrder2Service;

    @Resource
    private WorkOrder2Mapper workOrder2Mapper;

    @Resource
    private WorkOrderEventMapper workOrderEventMapper;

    @Resource
    private BatchOperationLogMapper batchOperationLogMapper;

    @MockBean
    private BatchOperationLogService batchOperationLogService;

    @Test
    public void testBatchRecoverWorkOrders_Success() {
        // 准备测试数据
        String batchId = "test-batch-001";
        
        // Mock 批量操作日志服务
        BatchOperationLogDO originalBatchLog = BatchOperationLogDO.builder()
                .id(1L)
                .batchId(batchId)
                .operationType(BatchOperationTypeEnum.BATCH_REJECT.getCode())
                .status(BatchOperationStatusEnum.COMPLETED.getCode())
                .processedCount(2)
                .build();
        
        when(batchOperationLogService.getBatchOperationLogByBatchId(batchId)).thenReturn(originalBatchLog);
        when(batchOperationLogService.createBatchOperationLog(any())).thenReturn(1L);
        
        // 创建被行政拒绝的工单
        WorkOrder2DO workOrder1 = createWorkOrder("123456789012345678", WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus());
        workOrder1.setOriginalStatus(WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        workOrder1.setBatchOperationId(batchId);
        
        WorkOrder2DO workOrder2 = createWorkOrder("123456789012345679", WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus());
        workOrder2.setOriginalStatus(WorkOrderStatusEnum.WAIT_PROCESSING.getStatus());
        workOrder2.setBatchOperationId(batchId);
        
        // 创建不相关的工单（不同批次号）
        WorkOrder2DO workOrder3 = createWorkOrder("123456789012345680", WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus());
        workOrder3.setOriginalStatus(WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        workOrder3.setBatchOperationId("other-batch-001");
        
        workOrder2Mapper.insert(workOrder1);
        workOrder2Mapper.insert(workOrder2);
        workOrder2Mapper.insert(workOrder3);

        // 执行批量恢复操作
        BatchRecoverRespVO result = workOrder2Service.batchRecoverWorkOrders(batchId);

        // 验证结果
        assertNotNull(result);
        assertEquals(batchId, result.getBatchId());
        assertEquals(2, result.getProcessedCount()); // 只有2个工单符合条件

        // 验证工单状态已恢复
        WorkOrder2DO recoveredWorkOrder1 = workOrder2Mapper.selectById(workOrder1.getId());
        WorkOrder2DO recoveredWorkOrder2 = workOrder2Mapper.selectById(workOrder2.getId());
        WorkOrder2DO unchangedWorkOrder3 = workOrder2Mapper.selectById(workOrder3.getId());

        // 符合条件的工单应该被恢复
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), recoveredWorkOrder1.getStatus());
        assertNull(recoveredWorkOrder1.getOriginalStatus());
        assertNull(recoveredWorkOrder1.getBatchOperationId());

        assertEquals(WorkOrderStatusEnum.WAIT_PROCESSING.getStatus(), recoveredWorkOrder2.getStatus());
        assertNull(recoveredWorkOrder2.getOriginalStatus());
        assertNull(recoveredWorkOrder2.getBatchOperationId());

        // 不相关的工单应该保持不变
        assertEquals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(), unchangedWorkOrder3.getStatus());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), unchangedWorkOrder3.getOriginalStatus());
        assertEquals("other-batch-001", unchangedWorkOrder3.getBatchOperationId());

        // 验证工单事件已创建
        List<WorkOrderEventDo> events1 = workOrderEventMapper.getWorkOrderEventList(workOrder1.getId());
        List<WorkOrderEventDo> events2 = workOrderEventMapper.getWorkOrderEventList(workOrder2.getId());
        
        assertTrue(events1.stream().anyMatch(e -> e.getType().equals(WorkOrderStatusEnum.WAIT_TAKING.getStatus())));
        assertTrue(events2.stream().anyMatch(e -> e.getType().equals(WorkOrderStatusEnum.WAIT_PROCESSING.getStatus())));
    }

    @Test
    public void testBatchRecoverWorkOrders_BatchNotFound() {
        // 准备测试数据
        String nonExistentBatchId = "non-existent-batch";
        
        // Mock 批量操作日志服务返回null
        when(batchOperationLogService.getBatchOperationLogByBatchId(nonExistentBatchId)).thenReturn(null);

        // 执行批量恢复操作并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            workOrder2Service.batchRecoverWorkOrders(nonExistentBatchId);
        });

        assertEquals(404, exception.getCode());
        assertTrue(exception.getMessage().contains("批次操作不存在"));
    }

    @Test
    public void testBatchRecoverWorkOrders_WrongOperationType() {
        // 准备测试数据
        String batchId = "test-batch-002";
        
        // Mock 批量操作日志服务返回非BATCH_REJECT类型的操作
        BatchOperationLogDO wrongTypeBatchLog = BatchOperationLogDO.builder()
                .id(1L)
                .batchId(batchId)
                .operationType("OTHER_OPERATION")
                .status(BatchOperationStatusEnum.COMPLETED.getCode())
                .processedCount(1)
                .build();
        
        when(batchOperationLogService.getBatchOperationLogByBatchId(batchId)).thenReturn(wrongTypeBatchLog);

        // 执行批量恢复操作并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            workOrder2Service.batchRecoverWorkOrders(batchId);
        });

        assertEquals(422, exception.getCode());
        assertTrue(exception.getMessage().contains("只能恢复批量拒绝操作"));
    }

    @Test
    public void testBatchRecoverWorkOrders_NoMatchingWorkOrders() {
        // 准备测试数据
        String batchId = "test-batch-003";
        
        // Mock 批量操作日志服务
        BatchOperationLogDO originalBatchLog = BatchOperationLogDO.builder()
                .id(1L)
                .batchId(batchId)
                .operationType(BatchOperationTypeEnum.BATCH_REJECT.getCode())
                .status(BatchOperationStatusEnum.COMPLETED.getCode())
                .processedCount(0)
                .build();
        
        when(batchOperationLogService.getBatchOperationLogByBatchId(batchId)).thenReturn(originalBatchLog);
        when(batchOperationLogService.createBatchOperationLog(any())).thenReturn(1L);

        // 执行批量恢复操作
        BatchRecoverRespVO result = workOrder2Service.batchRecoverWorkOrders(batchId);

        // 验证结果
        assertNotNull(result);
        assertEquals(batchId, result.getBatchId());
        assertEquals(0, result.getProcessedCount()); // 没有工单被处理
    }

    private WorkOrder2DO createWorkOrder(String idCardNumber, Integer status) {
        return WorkOrder2DO.builder()
                .hospitalCode("TEST001")
                .hospitalName("测试医院")
                .treatmentSerialNumberType(0)
                .treatmentSerialNumber("TEST" + System.currentTimeMillis())
                .name("测试用户")
                .idCardNumber(idCardNumber)
                .mobilePhoneNumber("13800138000")
                .treatmentDatetime(new Date())
                .status(status)
                .build();
    }
}
