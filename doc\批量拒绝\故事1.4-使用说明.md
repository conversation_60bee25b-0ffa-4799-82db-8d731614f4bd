# 故事 1.4 - 批量操作日志抽屉使用说明

## 功能概述

批量操作日志抽屉是工单管理页面的新增功能，允许用户快速查看和查询所有批量操作的历史记录，包括批量拒绝和批量恢复操作。

## 使用步骤

### 1. 打开批量操作日志

1. 进入工单管理页面（`/insurance/workOrder`）
2. 在页面顶部操作工具栏中，找到"批量操作日志"按钮
3. 点击按钮，抽屉将从页面右侧滑出

### 2. 查看操作记录

抽屉打开后，会自动加载最新的批量操作记录，表格包含以下信息：

- **批次号**：每次批量操作的唯一标识
- **操作类型**：批量拒绝或批量恢复
- **状态**：执行中（黄色）、已完成（绿色）、执行失败（红色）
- **处理数量**：本次操作处理的工单数量
- **操作员**：执行操作的用户姓名
- **开始时间**：操作开始的时间
- **结束时间**：操作完成的时间
- **执行时长**：操作耗时（自动格式化为小时/分钟/秒）
- **备注**：操作的详细说明或错误信息

### 3. 搜索和筛选

#### 3.1 按操作类型筛选
- 选择"批量拒绝"查看所有拒绝操作
- 选择"批量恢复"查看所有恢复操作
- 留空显示所有类型的操作

#### 3.2 按操作状态筛选
- 选择"执行中"查看正在进行的操作
- 选择"已完成"查看成功完成的操作
- 选择"执行失败"查看失败的操作

#### 3.3 按操作员搜索
- 输入操作员姓名进行模糊搜索
- 支持部分匹配

#### 3.4 按时间范围筛选
- 选择开始时间和结束时间
- 查找指定时间段内的操作记录

#### 3.5 执行搜索
- 设置好筛选条件后，点击"搜索"按钮
- 点击"重置"按钮清空所有筛选条件

### 4. 分页浏览

- 使用底部的分页组件浏览更多记录
- 可以调整每页显示的记录数量
- 支持跳转到指定页码

### 5. 关闭抽屉

- 点击抽屉右上角的关闭按钮（X）
- 或者点击抽屉外的蒙层区域
- 抽屉关闭后不会影响工单管理页面的状态

## 使用场景

### 1. 操作审计
- 查看谁在什么时间执行了批量操作
- 了解操作的处理数量和结果
- 追踪操作失败的原因

### 2. 问题排查
- 当工单状态异常时，查看相关的批量操作记录
- 通过批次号关联具体的工单变更
- 分析操作失败的时间和原因

### 3. 操作统计
- 统计某个时间段内的批量操作次数
- 了解不同操作员的操作频率
- 分析批量操作的成功率

### 4. 合规要求
- 满足操作日志记录的合规要求
- 提供完整的操作审计轨迹
- 支持监管部门的检查需求

## 权限要求

使用批量操作日志功能需要以下权限：
- `insurance:work-order:query` - 工单查询权限

## 注意事项

1. **数据实时性**：日志数据实时更新，反映最新的操作状态
2. **性能优化**：采用分页加载，避免一次性加载大量数据
3. **数据安全**：只能查看有权限的操作记录
4. **操作独立**：抽屉内的操作不会影响主页面的工单列表

## 常见问题

### Q: 为什么看不到某些操作记录？
A: 请检查以下几点：
- 确认有相应的查询权限
- 检查时间范围筛选是否过于严格
- 确认操作确实已经执行（可能还在执行中）

### Q: 如何找到特定批次的操作记录？
A: 可以通过以下方式：
- 在搜索条件中输入批次号的部分内容
- 按操作时间范围缩小搜索范围
- 按操作员姓名筛选

### Q: 执行时长显示为"-"是什么意思？
A: 这表示操作还在执行中，尚未完成，因此无法计算执行时长。

### Q: 抽屉加载很慢怎么办？
A: 建议：
- 缩小时间范围查询
- 使用更具体的筛选条件
- 检查网络连接状态

## 技术支持

如果在使用过程中遇到问题，请联系技术支持团队，并提供以下信息：
- 具体的错误信息或异常现象
- 操作步骤和时间
- 使用的浏览器和版本
- 相关的批次号或操作记录
