package cn.iocoder.yudao.module.insurance.dal.mysql.batchoperationlog;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 批量操作日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchOperationLogMapper extends BaseMapperX<BatchOperationLogDO> {

    /**
     * 根据批次号查询批量操作日志
     *
     * @param batchId 批次号
     * @return 批量操作日志
     */
    default BatchOperationLogDO selectByBatchId(String batchId) {
        return selectOne(new LambdaQueryWrapperX<BatchOperationLogDO>()
                .eq(BatchOperationLogDO::getBatchId, batchId));
    }
}
