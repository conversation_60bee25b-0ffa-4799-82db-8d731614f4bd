package cn.iocoder.yudao.module.insurance.enums.batchoperation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 批量操作类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BatchOperationTypeEnum {
    
    BATCH_REJECT("BATCH_REJECT", "批量拒绝"),
    BATCH_RECOVER("BATCH_RECOVER", "批量恢复");
    
    /**
     * 操作类型代码
     */
    private final String code;
    
    /**
     * 操作类型名称
     */
    private final String name;
}
