
8de3a4b5153ddefdc7194efd0da57e8a1d6402c8	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"36f656e330e2bf3c429747489cf25bff\"}","integrity":"sha512-lBuMO+5pnFzUDprGSSvFgjzToRsb5zQ+gx5XVY190cN1V6WSopM+RcDm15wgwJ6r/+akuRs/MtZ82k+obkgZ0Q==","time":1754285660580,"size":16741549}