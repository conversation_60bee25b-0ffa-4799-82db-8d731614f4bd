package cn.iocoder.yudao.module.insurance.service.workorder2;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.BatchRecoverRespVO;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.BatchRejectRespVO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderEventDo;
import cn.iocoder.yudao.module.insurance.dal.mysql.batchoperationlog.BatchOperationLogMapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrder2Mapper;
import cn.iocoder.yudao.module.insurance.dal.mysql.workorder2.WorkOrderEventMapper;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationStatusEnum;
import cn.iocoder.yudao.module.insurance.enums.batchoperation.BatchOperationTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderStatusEnum;
import cn.iocoder.yudao.module.insurance.service.batchoperationlog.BatchOperationLogService;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 批量操作集成测试 - 测试批量拒绝和恢复的完整流程
 *
 * <AUTHOR>
 */
@Import({WorkOrder2ServiceImpl.class})
public class BatchOperationIntegrationTest extends BaseDbUnitTest {

    @Resource
    private WorkOrder2Service workOrder2Service;

    @Resource
    private WorkOrder2Mapper workOrder2Mapper;

    @Resource
    private WorkOrderEventMapper workOrderEventMapper;

    @Resource
    private BatchOperationLogMapper batchOperationLogMapper;

    @Resource
    private BatchOperationLogService batchOperationLogService;

    @Test
    public void testCompleteWorkflow_BatchRejectAndRecover() {
        // 第一步：准备测试数据
        Date cutoffDate = new Date();
        Date treatmentDate = new Date(cutoffDate.getTime() - 24 * 60 * 60 * 1000); // 昨天
        
        // 创建待接单的工单
        WorkOrder2DO workOrder1 = createWorkOrder("123456789012345678", treatmentDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        WorkOrder2DO workOrder2 = createWorkOrder("123456789012345679", treatmentDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        
        workOrder2Mapper.insert(workOrder1);
        workOrder2Mapper.insert(workOrder2);

        // 第二步：执行批量拒绝操作
        BatchRejectRespVO rejectResult = workOrder2Service.batchRejectWorkOrders(cutoffDate);
        
        // 验证批量拒绝结果
        assertNotNull(rejectResult);
        assertNotNull(rejectResult.getBatchId());
        assertEquals(2, rejectResult.getProcessedCount());

        // 验证工单状态已更新为行政拒绝
        WorkOrder2DO rejectedWorkOrder1 = workOrder2Mapper.selectById(workOrder1.getId());
        WorkOrder2DO rejectedWorkOrder2 = workOrder2Mapper.selectById(workOrder2.getId());

        assertEquals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(), rejectedWorkOrder1.getStatus());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), rejectedWorkOrder1.getOriginalStatus());
        assertEquals(rejectResult.getBatchId(), rejectedWorkOrder1.getBatchOperationId());

        assertEquals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus(), rejectedWorkOrder2.getStatus());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), rejectedWorkOrder2.getOriginalStatus());
        assertEquals(rejectResult.getBatchId(), rejectedWorkOrder2.getBatchOperationId());

        // 验证批量拒绝的审计日志
        BatchOperationLogDO rejectLog = batchOperationLogService.getBatchOperationLogByBatchId(rejectResult.getBatchId());
        assertNotNull(rejectLog);
        assertEquals(BatchOperationTypeEnum.BATCH_REJECT.getCode(), rejectLog.getOperationType());
        assertEquals(BatchOperationStatusEnum.COMPLETED.getCode(), rejectLog.getStatus());
        assertEquals(2, rejectLog.getProcessedCount());

        // 验证工单事件记录
        List<WorkOrderEventDo> events1 = workOrderEventMapper.getWorkOrderEventList(workOrder1.getId());
        List<WorkOrderEventDo> events2 = workOrderEventMapper.getWorkOrderEventList(workOrder2.getId());
        
        assertTrue(events1.stream().anyMatch(e -> e.getType().equals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus())));
        assertTrue(events2.stream().anyMatch(e -> e.getType().equals(WorkOrderStatusEnum.ADMINISTRATIVELY_REJECTED.getStatus())));

        // 第三步：执行批量恢复操作
        BatchRecoverRespVO recoverResult = workOrder2Service.batchRecoverWorkOrders(rejectResult.getBatchId());
        
        // 验证批量恢复结果
        assertNotNull(recoverResult);
        assertEquals(rejectResult.getBatchId(), recoverResult.getBatchId());
        assertEquals(2, recoverResult.getProcessedCount());

        // 验证工单状态已恢复
        WorkOrder2DO recoveredWorkOrder1 = workOrder2Mapper.selectById(workOrder1.getId());
        WorkOrder2DO recoveredWorkOrder2 = workOrder2Mapper.selectById(workOrder2.getId());

        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), recoveredWorkOrder1.getStatus());
        assertNull(recoveredWorkOrder1.getOriginalStatus());
        assertNull(recoveredWorkOrder1.getBatchOperationId());

        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), recoveredWorkOrder2.getStatus());
        assertNull(recoveredWorkOrder2.getOriginalStatus());
        assertNull(recoveredWorkOrder2.getBatchOperationId());

        // 验证恢复操作的审计日志
        List<BatchOperationLogDO> allLogs = batchOperationLogMapper.selectList(null);
        BatchOperationLogDO recoverLog = allLogs.stream()
                .filter(log -> BatchOperationTypeEnum.BATCH_RECOVER.getCode().equals(log.getOperationType()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(recoverLog);
        assertEquals(BatchOperationStatusEnum.COMPLETED.getCode(), recoverLog.getStatus());
        assertEquals(2, recoverLog.getProcessedCount());
        assertTrue(recoverLog.getRemarks().contains(rejectResult.getBatchId()));

        // 验证恢复后的工单事件记录
        List<WorkOrderEventDo> finalEvents1 = workOrderEventMapper.getWorkOrderEventList(workOrder1.getId());
        List<WorkOrderEventDo> finalEvents2 = workOrderEventMapper.getWorkOrderEventList(workOrder2.getId());
        
        // 应该有两个事件：拒绝事件和恢复事件
        assertEquals(2, finalEvents1.size());
        assertEquals(2, finalEvents2.size());
        
        // 最后一个事件应该是恢复到待接单状态
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), 
                finalEvents1.get(finalEvents1.size() - 1).getType());
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), 
                finalEvents2.get(finalEvents2.size() - 1).getType());
    }

    @Test
    public void testPartialRecovery_SomeWorkOrdersAlreadyChanged() {
        // 准备测试数据
        Date cutoffDate = new Date();
        Date treatmentDate = new Date(cutoffDate.getTime() - 24 * 60 * 60 * 1000);
        
        WorkOrder2DO workOrder1 = createWorkOrder("123456789012345678", treatmentDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        WorkOrder2DO workOrder2 = createWorkOrder("123456789012345679", treatmentDate, WorkOrderStatusEnum.WAIT_TAKING.getStatus());
        
        workOrder2Mapper.insert(workOrder1);
        workOrder2Mapper.insert(workOrder2);

        // 执行批量拒绝
        BatchRejectRespVO rejectResult = workOrder2Service.batchRejectWorkOrders(cutoffDate);
        assertEquals(2, rejectResult.getProcessedCount());

        // 手动修改其中一个工单的状态（模拟其他操作）
        WorkOrder2DO modifiedWorkOrder = workOrder2Mapper.selectById(workOrder1.getId());
        modifiedWorkOrder.setStatus(WorkOrderStatusEnum.FINISHED.getStatus());
        workOrder2Mapper.updateById(modifiedWorkOrder);

        // 执行批量恢复
        BatchRecoverRespVO recoverResult = workOrder2Service.batchRecoverWorkOrders(rejectResult.getBatchId());
        
        // 只有一个工单应该被恢复（另一个已经不是行政拒绝状态）
        assertEquals(1, recoverResult.getProcessedCount());

        // 验证结果
        WorkOrder2DO finalWorkOrder1 = workOrder2Mapper.selectById(workOrder1.getId());
        WorkOrder2DO finalWorkOrder2 = workOrder2Mapper.selectById(workOrder2.getId());

        // 第一个工单应该保持已完成状态（不被恢复）
        assertEquals(WorkOrderStatusEnum.FINISHED.getStatus(), finalWorkOrder1.getStatus());
        
        // 第二个工单应该被恢复
        assertEquals(WorkOrderStatusEnum.WAIT_TAKING.getStatus(), finalWorkOrder2.getStatus());
        assertNull(finalWorkOrder2.getOriginalStatus());
        assertNull(finalWorkOrder2.getBatchOperationId());
    }

    private WorkOrder2DO createWorkOrder(String idCardNumber, Date treatmentDate, Integer status) {
        return WorkOrder2DO.builder()
                .hospitalCode("TEST001")
                .hospitalName("测试医院")
                .treatmentSerialNumberType(0)
                .treatmentSerialNumber("TEST" + System.currentTimeMillis())
                .name("测试用户")
                .idCardNumber(idCardNumber)
                .mobilePhoneNumber("13800138000")
                .treatmentDatetime(treatmentDate)
                .status(status)
                .build();
    }
}
