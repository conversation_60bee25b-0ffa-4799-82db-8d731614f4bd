
69425311fd34c3982e47c92c5080be010504e17a	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"f249558c9febb3bf13558494feba697b\"}","integrity":"sha512-tXn1DFcxNYXKvgtuBTFyLgRkTcYCNDYCF/ienFQA6fAtlupLjmGbfDUeOdu0cFWXmvBmahUPSH7eWHU17SaCqw==","time":1754285659643,"size":3473649}