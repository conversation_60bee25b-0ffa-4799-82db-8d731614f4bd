package cn.iocoder.yudao.module.insurance.enums.batchoperation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 批量操作状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BatchOperationStatusEnum {
    
    RUNNING("RUNNING", "执行中"),
    COMPLETED("COMPLETED", "已完成"),
    FAILED("FAILED", "执行失败");
    
    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
}
