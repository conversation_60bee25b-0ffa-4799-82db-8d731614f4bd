package cn.iocoder.yudao.module.insurance.service.batchoperationlog;

import cn.iocoder.yudao.module.insurance.dal.dataobject.batchoperationlog.BatchOperationLogDO;

/**
 * 批量操作日志 Service 接口
 *
 * <AUTHOR>
 */
public interface BatchOperationLogService {

    /**
     * 创建批量操作日志
     *
     * @param batchOperationLog 批量操作日志
     * @return 编号
     */
    Long createBatchOperationLog(BatchOperationLogDO batchOperationLog);

    /**
     * 更新批量操作日志
     *
     * @param batchOperationLog 批量操作日志
     */
    void updateBatchOperationLog(BatchOperationLogDO batchOperationLog);

    /**
     * 根据批次号获取批量操作日志
     *
     * @param batchId 批次号
     * @return 批量操作日志
     */
    BatchOperationLogDO getBatchOperationLogByBatchId(String batchId);

    /**
     * 获得批量操作日志
     *
     * @param id 编号
     * @return 批量操作日志
     */
    BatchOperationLogDO getBatchOperationLog(Long id);
}
